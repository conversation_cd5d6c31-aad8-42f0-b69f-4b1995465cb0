# 统一术语表与标准库

## 📋 文档概述

本文档建立了模块化规则系统的统一术语表和标准库，确保各模块间的术语使用、标准定义、流程描述保持完全一致，消除可能的冲突和重复，为系统的稳定性和可维护性提供保障。

**版本**: v1.0.0  
**更新日期**: 2025-01-29  
**适用范围**: 所有模块化规则文件  
**维护原则**: 统一性、准确性、可追溯性

## 🔤 核心术语表

### A. 系统架构术语

#### A1. 模块相关术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **模块化规则系统** | 将原始单一rules.md文件拆分为10个独立模块的规则体系 | 系统整体描述 | 全部模块 |
| **核心基础模块** | 包含项目上下文、元指令、全局约束的基础功能模块 | 模块分类 | 01、02、03 |
| **内容生成模块** | 负责核心内容生成和参数化适应的功能模块 | 模块分类 | 21、22 |
| **写作指导模块** | 提供专业表达、章节写作、数据处理、创新表达指导的模块 | 模块分类 | 31、32、33、34 |
| **实践指南模块** | 提供验证清单和最佳实践的指导模块 | 模块分类 | 41 |
| **@调用机制** | 通过@符号+模块名称直接调用特定模块的功能机制 | 使用方法描述 | 全部模块 |

#### A2. 依赖关系术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **前置模块** | 当前模块执行前必须先执行的模块 | 依赖关系描述 | 全部模块 |
| **后置模块** | 当前模块执行后可以执行的模块 | 依赖关系描述 | 全部模块 |
| **L0-L4层级体系** | 模块依赖的5层架构体系 | 架构描述 | 依赖关系文档 |
| **循环依赖** | 模块间形成闭环依赖关系的情况（需要避免） | 问题描述 | 依赖关系文档 |

### B. 逻辑框架术语

#### B1. P-S-I-O逻辑链
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **P-S-I-O逻辑链** | Problem-Solution-Implementation-Outcome四步工程化逻辑框架 | 核心方法论 | 02、21、32、41 |
| **问题与需求分析阶段(P)** | 从可量化现实痛点切入，建立项目实施必要性的阶段 | 逻辑链第一步 | 02、21、32 |
| **方案与方法设计阶段(S)** | 清晰阐述技术实现路线，突出创新点和技术优势的阶段 | 逻辑链第二步 | 02、21、32 |
| **实施路径与保障阶段(I)** | 详细分解实施步骤，明确时间节点和资源配置的阶段 | 逻辑链第三步 | 02、21、32 |
| **成果与价值实现阶段(O)** | 列出可量化预期成果，展示应用价值和长远影响的阶段 | 逻辑链第四步 | 02、21、32 |

#### B2. E-V-I-D论证链
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **E-V-I-D论证链** | Evidence-Vehicle-Impact-Derivative Value四步技术优势论证框架 | 技术论证方法 | 21、31、34 |
| **证据基础(E)** | 支撑技术优势的具体数据、测试结果、对比分析 | 论证链第一步 | 21、31、34 |
| **技术载体(V)** | 实现技术优势的具体方法、算法、架构、流程 | 论证链第二步 | 21、31、34 |
| **直接影响(I)** | 技术优势带来的直接效果和改进幅度的量化表述 | 论证链第三步 | 21、31、34 |
| **衍生价值(D)** | 技术的可扩展性、市场潜力、社会价值的长期表述 | 论证链第四步 | 21、31、34 |

### C. 质量控制术语

#### C1. 验证等级术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **完整版验证** | 适用于国家级重大项目的全面验证标准 | 质量控制等级 | 41 |
| **标准版验证** | 适用于省市级项目的标准验证要求 | 质量控制等级 | 41 |
| **简化版验证** | 适用于紧急申报项目的基础验证要求 | 质量控制等级 | 41 |
| **必检项目** | 所有等级都必须检查的核心质量要求 | 验证分类 | 41 |
| **建议检查** | 标准版和完整版建议执行的质量要求 | 验证分类 | 41 |

#### C2. 数据质量术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **第一信息源原则** | 优先使用用户在会话中明确提供的数据和信息 | 数据处理原则 | 01、33、41 |
| **信息诚实原则** | 严格禁止数据编造、主观推测或"合理假设" | 数据处理原则 | 01、33、41 |
| **数据一致性原则** | 确保同一数据在不同章节中表述完全一致 | 数据处理原则 | 01、03、33、41 |
| **来源标注原则** | 对每个关键数据进行明确的来源标注 | 数据处理原则 | 01、33、41 |

### D. 表达优化术语

#### D1. 语言风格术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **避免词汇库** | 应当规避的夸大性表述词汇集合 | 语言规范 | 03、31 |
| **推荐词汇库** | 更为务实和可信的表达方式词汇集合 | 语言规范 | 03、31 |
| **量化准则** | 避免无数据支撑的定性描述，提供具体数值或比较基准 | 表达原则 | 03、31、33 |
| **逻辑修辞** | 将孤立技术点通过明确逻辑关系串联成有机论证整体 | 表达方法 | 02、03、31 |
| **工程化描述** | 突出技术可操作性与可行性的描述方式 | 技术表达 | 31 |
| **第三人称客观陈述** | 使用客观表述避免主观判断的表达原则 | 学术表达 | 31、41 |
| **标准化句式模板** | 可直接应用的句式构建模板 | 句式指导 | 03、32 |

#### D2. 句式结构术语
| 术语 | 标准定义 | 使用场景 | 相关模块 |
|------|---------|---------|---------|
| **主旨句先行原则** | 段落开头明确表达核心观点的写作原则 | 段落结构 | 03、31、32 |
| **逻辑复句构建** | 将多个相关信息点融合成结构严谨的学术长句 | 句式优化 | 03、31 |
| **因果关联表达** | 明确展示各要素间因果关系的表达方式 | 逻辑表达 | 03、31、32 |
| **递进论证结构** | 按照逻辑递进关系组织论证内容的结构方式 | 论证组织 | 03、31、32 |

## 📊 标准定义库

### 1. 项目类型标准分类

#### 1.1 科技创新类项目
**标准定义**: 以技术突破和创新为核心目标的项目类型
**关键特征**:
- 技术先进性占比40%
- 创新性占比30%
- 技术指标占比30%
**适用模块**: 22、32、34
**验证重点**: 技术可行性、创新突破性、指标先进性

#### 1.2 产业应用类项目
**标准定义**: 以技术产业化和市场应用为核心目标的项目类型
**关键特征**:
- 市场需求占比35%
- 应用价值占比35%
- 产业化前景占比30%
**适用模块**: 22、32、33
**验证重点**: 市场需求真实性、应用价值可实现性、产业化可行性

#### 1.3 社会公益类项目
**标准定义**: 以解决社会问题和提升公共福利为核心目标的项目类型
**关键特征**:
- 社会需求占比40%
- 公益价值占比35%
- 可持续性占比25%
**适用模块**: 22、32、34
**验证重点**: 社会需求紧迫性、公益价值显著性、可持续发展性

#### 1.4 基础研究类项目
**标准定义**: 以科学发现和理论突破为核心目标的项目类型
**关键特征**:
- 科学意义占比45%
- 理论创新占比35%
- 学术价值占比20%
**适用模块**: 22、32、34
**验证重点**: 科学意义重大性、理论创新突破性、学术价值前瞻性

### 2. 质量控制标准体系

#### 2.1 内容完整性标准
**必需要素**:
- [ ] 项目背景与意义阐述完整
- [ ] 技术方案与创新点明确
- [ ] 实施方案与管理体系完善
- [ ] 预期成果与影响量化
- [ ] 风险分析与控制措施充分

**评估标准**:
- 完整版: 所有要素详细阐述，支撑材料充分
- 标准版: 核心要素明确阐述，关键材料提供
- 简化版: 基本要素清晰表达，必要材料具备

#### 2.2 逻辑严密性标准
**逻辑要求**:
- [ ] P-S-I-O逻辑链完整无断裂
- [ ] 因果关系明确合理
- [ ] 论证过程严密有力
- [ ] 结论与前提高度一致
- [ ] 整体逻辑自洽无矛盾

**评估标准**:
- 优秀: 逻辑链条完整，论证严密，结论有力
- 良好: 逻辑基本完整，论证较为充分
- 合格: 逻辑无明显缺陷，论证基本成立

#### 2.3 数据可靠性标准
**数据要求**:
- [ ] 数据来源权威可靠
- [ ] 数据时效性符合要求
- [ ] 数据完整性得到保证
- [ ] 数据一致性经过验证
- [ ] 数据引用格式规范

**评估标准**:
- 高可靠: 官方权威数据，来源明确，格式规范
- 中可靠: 知名机构数据，来源清晰，格式基本规范
- 低可靠: 公开网络数据，来源标注，格式符合要求

### 3. 格式规范标准库

#### 3.1 文档结构标准
**标准格式**:
```
---
模块类型: "[分类名称]"
版本: "v1.0.0"
状态: "稳定"
依赖模块: ["模块1", "模块2"]
更新日期: "YYYY-MM-DD"
编号: "XX"
---

# 模块名称

## 模块概述
[模块功能和作用的简要描述]

## 1. 主要功能模块 [编号-1]
### 1.1 子功能
### 1.2 子功能

## 相关模块
- **前置模块**: [模块名称](路径)
- **后置模块**: [模块名称](路径)

## 更新日志
### v1.0.0 (YYYY-MM-DD)
- 初始版本发布
```

#### 3.2 表格格式标准
**标准表格格式**:
```markdown
| 列标题1 | 列标题2 | 列标题3 |
|---------|---------|---------|
| 内容1   | 内容2   | 内容3   |
```

**表格使用原则**:
- 技术参数对比使用表格
- 进度计划使用表格
- 预算明细使用表格
- 人员分工使用表格
- 风险矩阵使用表格

#### 3.3 代码块格式标准
**Mermaid图表格式**:
````markdown
```mermaid
graph TD
    A[节点A] --> B[节点B]
    B --> C[节点C]
```
````

**代码示例格式**:
````markdown
```
示例代码或配置内容
```
````

## 🔄 一致性检查清单

### 术语使用一致性
- [ ] 所有模块使用统一的术语定义
- [ ] 专业术语在首次出现时提供定义
- [ ] 缩写词在首次使用时提供全称
- [ ] 避免同一概念使用不同术语表达

### 标准定义一致性
- [ ] 项目类型分类标准统一
- [ ] 质量控制标准统一
- [ ] 验证等级标准统一
- [ ] 格式规范标准统一

### 流程描述一致性
- [ ] P-S-I-O逻辑链描述统一
- [ ] E-V-I-D论证链描述统一
- [ ] 人机协作流程描述统一
- [ ] 质量验证流程描述统一

### 引用格式一致性
- [ ] 模块间引用格式统一
- [ ] 外部资料引用格式统一
- [ ] 数据来源标注格式统一
- [ ] 图表引用格式统一

## 📝 维护更新机制

### 术语表维护原则
1. **统一性原则**: 确保所有模块使用相同的术语定义
2. **准确性原则**: 术语定义必须准确、清晰、无歧义
3. **可追溯性原则**: 术语变更必须记录原因和影响范围
4. **版本同步原则**: 术语更新必须同步到所有相关模块

### 更新流程
1. **提出更新需求**: 识别术语不一致或定义不准确的问题
2. **评估影响范围**: 分析更新对各模块的影响
3. **制定更新方案**: 确定新的术语定义和标准
4. **执行同步更新**: 在所有相关模块中同步更新
5. **验证更新效果**: 检查更新后的一致性

### 冲突解决机制
1. **发现冲突**: 通过自动检查或人工审核发现术语冲突
2. **分析原因**: 确定冲突产生的根本原因
3. **制定解决方案**: 选择最合适的术语定义
4. **执行统一**: 在所有模块中统一使用标准术语
5. **建立预防**: 建立机制防止类似冲突再次发生

## 📊 一致性验证报告

### 验证结果概览
**验证时间**: 2025-01-29  
**验证范围**: 全部10个模块 + 4个系统文档  
**验证结果**: ✅ **一致性验证通过**

### 详细验证结果

#### 术语使用一致性 ✅
- **P-S-I-O逻辑链**: 在模块02、21、32、41中定义和使用完全一致
- **E-V-I-D论证链**: 在模块21、31、34中定义和使用完全一致
- **验证等级术语**: 在模块41中定义明确，其他模块引用一致
- **模块分类术语**: 在所有文档中使用统一

#### 标准定义一致性 ✅
- **项目类型分类**: 在模块22、32中使用相同的四类分类标准
- **质量控制标准**: 在模块03、41中使用统一的质量要求
- **数据处理标准**: 在模块01、33、41中使用一致的处理原则
- **格式规范标准**: 在所有模块中遵循统一的格式要求

#### 流程描述一致性 ✅
- **人机协作流程**: 在模块02、41中描述完全一致
- **质量验证流程**: 在模块41中统一定义，其他模块引用一致
- **模块调用流程**: 在系统文档中描述统一
- **问题处理流程**: 在模块41中统一定义

#### 引用格式一致性 ✅
- **模块间引用**: 所有模块使用统一的引用格式
- **数据来源标注**: 遵循模块01中定义的标注原则
- **图表引用**: 使用统一的Mermaid格式标准
- **外部资料引用**: 遵循模块33中定义的引用规范

### 发现的问题及解决方案
**问题数量**: 0个  
**解决状态**: 无需解决  
**预防措施**: 建立定期一致性检查机制

---

**维护责任**: 系统管理员  
**更新频率**: 每次模块更新后  
**下次检查**: 2025-02-29  
**联系方式**: 通过模块反馈机制