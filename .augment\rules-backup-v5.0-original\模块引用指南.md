# 模块引用快速指南

## 🎯 @调用语法

### 基础调用方式

#### 1. 直接模块名调用
```
@项目上下文与资源定义
@元指令与主算法
@全局约束与风格规范
@核心内容生成
@参数化适应
@专业表达优化
@章节专项写作指导
@数据处理与引用标准
@创新点识别与表达框架
@核心验证清单
```

#### 2. 编号调用方式
```
@01  # 项目上下文与资源定义
@02  # 元指令与主算法
@03  # 全局约束与风格规范
@21  # 核心内容生成
@22  # 参数化适应
@31  # 专业表达优化
@32  # 章节专项写作指导
@33  # 数据处理与引用标准
@34  # 创新点识别与表达框架
@41  # 核心验证清单
```

#### 3. 分类调用方式
```
@核心基础    # 调用模块01、02、03
@内容生成    # 调用模块21、22
@写作指导    # 调用模块31、32、33、34
@实践指南    # 调用模块41
```

## 🔄 常用组合调用

### 项目类型组合

#### 技术研发类项目
```
@项目上下文与资源定义 → @核心内容生成 → @创新点识别与表达框架 → @核心验证清单
```

#### 应用示范类项目
```
@参数化适应 → @章节专项写作指导 → @数据处理与引用标准 → @核心验证清单
```

#### 基础研究类项目
```
@元指令与主算法 → @专业表达优化 → @创新点识别与表达框架 → @核心验证清单
```

### 使用等级组合

#### 完整版使用流程
```
第一阶段：@01 + @02 + @03
第二阶段：@21 + @22
第三阶段：@31 + @32 + @33 + @34
第四阶段：@41 (完整版验证)
```

#### 标准版使用流程
```
核心阶段：@01 + @02 + @03 + @21 + @31
验证阶段：@41 (标准版验证)
```

#### 简化版使用流程
```
快速生成：@02 + @31
快速验证：@41 (简化版验证)
```

## 📋 场景化调用指南

### 申报准备阶段
```
材料整理：@项目上下文与资源定义
流程规划：@元指令与主算法
标准确认：@全局约束与风格规范
```

### 内容创作阶段
```
核心内容：@核心内容生成
项目适配：@参数化适应
表达优化：@专业表达优化
```

### 章节写作阶段
```
结构指导：@章节专项写作指导
数据处理：@数据处理与引用标准
创新表达：@创新点识别与表达框架
```

### 质量控制阶段
```
全面验证：@核心验证清单
```

## 🎨 高级调用技巧

### 1. 条件调用
```
如果是国家级项目 → @41 (完整版验证)
如果是省级项目 → @41 (标准版验证)
如果是紧急申报 → @41 (简化版验证)
```

### 2. 链式调用
```
@01 → 确认材料完整性 → @21 → 生成核心内容 → @31 → 优化表达 → @41 → 质量验证
```

### 3. 并行调用
```
同时参考：@32 (章节结构) + @33 (数据标准) + @34 (创新表达)
```

## 🔍 模块内容快速定位

### 按功能查找

#### 信息收集相关
- **@01**: 信息获取机制、数据处理准则
- **@33**: 数据收集、验证、引用标准

#### 逻辑构建相关
- **@02**: P-S-I-O逻辑链、人机协作流程
- **@21**: E-V-I-D论证链条、风险分析

#### 表达优化相关
- **@31**: 句式优化、逻辑修辞
- **@32**: 各章节写作模板
- **@34**: 创新点表达策略

#### 质量控制相关
- **@03**: 全局约束、风格规范
- **@41**: 验证清单、质量保证

### 按问题类型查找

#### 材料不足问题
```
@01 → 信息获取机制
@41 → 材料不足问题处理指南
```

#### 逻辑不清问题
```
@02 → P-S-I-O逻辑链
@21 → E-V-I-D论证链条
```

#### 表达不当问题
```
@31 → 专业表达优化
@32 → 章节专项写作指导
```

#### 数据问题
```
@33 → 数据处理与引用标准
@41 → 数据冲突问题处理
```

#### 创新不突出问题
```
@34 → 创新点识别与表达框架
@21 → 创新点提炼方法
```

## 📱 移动端调用优化

### 简化调用代码
```
@01  # 而不是 @项目上下文与资源定义
@核心  # 而不是 @核心内容生成
@验证  # 而不是 @核心验证清单
```

### 常用组合代码
```
@基础流程  # = @01 + @02 + @03
@内容创作  # = @21 + @22
@表达优化  # = @31 + @32
@质量控制  # = @41
```

## 🔧 自定义调用配置

### 项目特定配置

#### 技术类项目配置
```
默认模块：@01, @02, @21, @34, @41
可选模块：@22, @31, @33
跳过模块：@32 (如果不需要详细章节指导)
```

#### 应用类项目配置
```
默认模块：@01, @22, @32, @33, @41
可选模块：@02, @21, @31
重点模块：@22 (参数化适应)
```

#### 基础研究配置
```
默认模块：@02, @21, @31, @34, @41
可选模块：@01, @03, @32
重点模块：@34 (创新点识别)
```

## 📊 调用效果监控

### 使用统计
- 记录最常用的模块组合
- 分析不同项目类型的调用模式
- 优化调用路径和组合

### 效果评估
- 跟踪申报成功率
- 收集用户反馈
- 持续优化调用体验

## 💡 最佳实践建议

### 1. 渐进式调用
- 新手：从@41开始了解整体要求
- 进阶：使用标准组合@01+@02+@21+@31+@41
- 专家：自定义组合和验证流程

### 2. 情境化调用
- 根据申报时间选择使用等级
- 根据项目类型选择模块组合
- 根据团队经验调整调用深度

### 3. 质量优先原则
- 宁可少用模块但用精
- 确保每个调用的模块都充分执行
- 重视@41验证清单的使用

---

**提示**: 熟练掌握@调用语法后，您可以像使用专业工具一样高效地调用各个模块，显著提升申报书写作的效率和质量。