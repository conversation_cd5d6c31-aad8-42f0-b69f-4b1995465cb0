# Rules.md 模块化拆分命名规范

## 文件夹结构设计

```
.augment/rules/
├── 模块索引.md                    # 主索引文件
├── 模块命名规范.md                # 本文件
├── 核心基础/                      # 基础核心模块
│   ├── 项目上下文与资源定义.md
│   ├── 元指令与主算法.md
│   └── 全局约束与风格规范.md
├── 内容生成/                      # 内容生成模块
│   ├── 核心内容生成.md
│   ├── 参数化适应.md
│   └── 核心验证清单.md
├── 写作指导/                      # 写作指导模块
│   ├── 章节专项写作指导.md
│   ├── 数据处理与引用标准.md
│   └── 创新点识别与表达框架.md
└── 实践指南/                      # 实践指南模块
    └── 使用指南与最佳实践.md
```

## 中文命名规范

### 1. 文件命名原则

**【核心原则】简洁明确，便于@调用**
- 使用中文名称，直观易懂
- 避免过长的文件名（建议10字以内）
- 使用功能性描述，避免抽象概念
- 保持命名的一致性和逻辑性

### 2. 具体命名规则

#### 2.1 文件名格式
```
功能描述.md
```

**示例：**
- ✅ `项目上下文与资源定义.md`
- ✅ `核心内容生成.md`
- ✅ `数据处理与引用标准.md`
- ❌ `模块零项目上下文与核心资源定义规范.md` (过长)
- ❌ `module-a-meta-instructions.md` (英文)

#### 2.2 文件夹命名规则
```
功能分类/
```

**四大分类：**
- `核心基础/` - 系统基础功能模块
- `内容生成/` - 内容创建相关模块  
- `写作指导/` - 写作规范和指导模块
- `实践指南/` - 使用方法和最佳实践

### 3. @调用示例

#### 3.1 单模块调用
```
@项目上下文与资源定义.md
@核心内容生成.md
@全局约束与风格规范.md
```

#### 3.2 多模块调用
```
@核心内容生成.md @核心验证清单.md
@数据处理与引用标准.md @创新点识别与表达框架.md
```

#### 3.3 完整路径调用（如需要）
```
@c:\Users\<USER>\Desktop\慧眼行动\.augment\rules\核心基础\项目上下文与资源定义.md
@c:\Users\<USER>\Desktop\慧眼行动\.augment\rules\内容生成\核心内容生成.md
```

### 4. 模块编号体系

为便于管理和引用，采用分层编号：

#### 4.1 一级分类编号
- **00** - 索引和规范文件
- **10** - 核心基础模块
- **20** - 内容生成模块  
- **30** - 写作指导模块
- **40** - 实践指南模块

#### 4.2 具体模块编号
```
00-模块索引.md
00-模块命名规范.md

10-项目上下文与资源定义.md
11-元指令与主算法.md
12-全局约束与风格规范.md

20-核心内容生成.md
21-参数化适应.md
22-核心验证清单.md

30-章节专项写作指导.md
31-数据处理与引用标准.md
32-创新点识别与表达框架.md

40-使用指南与最佳实践.md
```

### 5. 版本控制规范

#### 5.1 版本号格式
```
模块名_v主版本.次版本.修订版本.md
```

**示例：**
- `项目上下文与资源定义_v1.0.0.md`
- `核心内容生成_v1.1.0.md`

#### 5.2 版本更新说明
- **主版本**：重大功能变更或结构调整
- **次版本**：新增功能或重要优化
- **修订版本**：错误修复或小幅改进

### 6. 特殊标记规范

#### 6.1 状态标记
- `[草稿]` - 正在编写中
- `[审核]` - 待审核确认
- `[稳定]` - 已确认可用
- `[废弃]` - 不再使用

#### 6.2 优先级标记
- `[核心]` - 核心必需模块
- `[重要]` - 重要功能模块
- `[可选]` - 可选扩展模块

### 7. 文件内容结构规范

每个模块文件应包含：

```markdown
---
模块类型: "核心基础/内容生成/写作指导/实践指南"
版本: "v1.0.0"
状态: "稳定"
依赖模块: ["模块1", "模块2"]
更新日期: "2025-01-XX"
---

# 模块名称

## 模块概述
[模块功能和作用的简要说明]

## 核心功能
[详细的功能描述]

## 使用方法
[具体的使用指导]

## 相关模块
[与其他模块的关联关系]

## 更新日志
[版本更新记录]
```

### 8. 质量控制标准

#### 8.1 命名检查清单
- [ ] 文件名是否简洁明确？
- [ ] 是否便于@调用？
- [ ] 是否符合中文命名规范？
- [ ] 是否与现有文件名冲突？
- [ ] 是否遵循分类逻辑？

#### 8.2 结构检查清单
- [ ] 文件夹结构是否合理？
- [ ] 模块分类是否清晰？
- [ ] 依赖关系是否明确？
- [ ] 版本控制是否规范？

## 实施建议

1. **渐进式迁移**：先创建新结构，再逐步迁移内容
2. **保持兼容**：迁移期间保留原文件，确保平滑过渡
3. **定期检查**：定期检查命名规范的执行情况
4. **用户反馈**：收集使用反馈，持续优化命名规范

这套命名规范确保了模块化拆分后的文件系统既专业规范，又便于日常使用和维护。