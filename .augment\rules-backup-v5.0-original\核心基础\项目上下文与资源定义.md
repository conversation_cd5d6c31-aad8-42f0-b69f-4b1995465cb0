---
type: "always_apply"
---

# 项目上下文与资源定义

## 模块概述

本模块构成整个规则体系的基础架构，其核心功能在于建立项目申报写作过程中信息获取、处理与应用的标准化框架。**作为最高优先级执行指令，任何后续模块的启动都必须以本模块所定义的上下文信息为前提条件，确保整个写作流程在统一的信息基础和处理准则下进行。**

## 1. 项目基本信息与适用范围界定

### 1.1 规则体系定位

本规则体系是一套通用的项目申报书撰写指导体系，当前版本为V5.0逻辑修辞融合版，该版本深度融合了学术化与专业化的表达原则，旨在将工程化的逻辑框架与高水平的修辞论证能力相结合，适用于各类科技项目、产业项目、社会公益项目等申报书的撰写工作。

### 1.2 适用范围矩阵

**适用范围涵盖四大主要项目申报类型：**

| 项目类型 | 适用程度 | 重点关注 | 特殊要求 |
|---------|---------|---------|---------|
| 科技创新类 | 完全适用 | 技术先进性、创新性 | 技术指标详细论证 |
| 产业应用类 | 完全适用 | 市场需求、应用价值 | 商业模式可行性 |
| 社会公益类 | 完全适用 | 社会效益、受益群体 | 可持续发展规划 |
| 基础研究类 | 完全适用 | 科学价值、理论贡献 | 学术影响力评估 |

### 1.3 模块化设计原理

通过模块化设计与参数化适应机制，能够根据不同项目类型的特征需求进行针对性的写作指导与质量控制：

- **模块化设计**：每个功能模块独立运行，便于维护和升级
- **参数化适应**：根据项目类型自动调整评估标准和表达重点
- **质量控制**：建立多层级验证机制，确保输出质量

## 2. 信息获取与使用方式

### 2.1 信息获取架构设计

信息获取机制采用分层级、多渠道的架构设计，通过建立明确的优先级序列和使用准则，确保项目申报材料的数据来源可靠性与内容准确性。

```mermaid
graph TD
    A[信息获取系统] --> B[第一优先级：用户提供材料]
    A --> C[第二优先级：公开行业数据]
    A --> D[第三优先级：图表参考资源]
    
    B --> B1[核心技术细节]
    B --> B2[真实测试数据]
    B --> B3[关键性能指标]
    B --> B4[实施方案]
    B --> B5[团队信息]
    
    C --> C1[行业统计数据]
    C --> C2[政策文件]
    C --> C3[技术标准]
    C --> C4[市场报告]
    
    D --> D1[图表示例]
    D --> D2[可视化需求]
    D --> D3[格式要求]
```

### 2.2 第一优先级：用户提供材料（User_Provided_Materials）

#### 2.2.1 获取方式详解
- **Markdown文档**：用户提供的已转换为md格式的各类申报材料
- **直接粘贴**：用户在会话中直接粘贴相关文本内容
- **数据描述**：用户口述或文字描述的技术参数、测试结果
- **结构化材料**：用户提供的表格化、列表化的数据和信息

#### 2.2.2 主要材料类型与处理方式

**申报材料类**：
| 材料类型 | 具体内容 | 使用要求 | 标注格式 |
|---------|---------|---------|---------|
| 现有申报书 | 已有的项目申报书、技术方案书 | 直接引用，保持精度 | "根据用户提供的申报材料" |
| 技术文档 | 技术规格书、设计文档、测试报告 | 准确引用，标注来源 | "根据用户提供的技术文档" |
| 数据资料 | 测试数据、性能指标、统计数据 | 保持数值精度，标注测试条件 | "根据用户提供的数据资料" |

**模板与规范类**：
| 材料类型 | 具体内容 | 使用要求 | 标注格式 |
|---------|---------|---------|---------|
| 申报书模板 | 官方申报书格式模板、填写要求 | 严格遵循格式要求 | "根据申报书模板要求" |
| 评审标准 | 评审指标、评分标准、申报指南 | 对照标准进行优化 | "根据评审标准要求" |
| 政策文件 | 相关政策、管理办法、实施细则 | 准确引用政策条款 | "根据相关政策文件" |

**调研与背景类**：
| 材料类型 | 具体内容 | 使用要求 | 标注格式 |
|---------|---------|---------|---------|
| 市场调研 | 市场分析、用户调研、竞品分析 | 结构化整理 | "根据用户提供的调研材料" |
| 行业报告 | 行业分析、技术趋势、发展现状 | 提取关键信息 | "根据用户提供的行业资料" |
| 团队信息 | 人员构成、专业背景、项目经验 | 准确反映团队能力 | "根据用户提供的团队资料" |

#### 2.2.3 三重保障机制
1. **优先使用原则**：优先使用用户明确提供的具体数据
2. **准确引用原则**：准确引用并标注来源，不得随意修改
3. **避免推测原则**：避免无根据的推测或补充

#### 2.2.4 具体操作步骤
```
步骤1：识别用户提供的材料类型和内容
步骤2：评估材料的完整性和可用性
步骤3：按照标注格式进行来源标记
步骤4：将材料内容整合到相应章节
步骤5：确保引用的准确性和一致性
```

### 2.3 第二优先级：公开行业数据（Public_Industry_Data）

#### 2.3.1 获取方式说明
基于AI训练数据中的公开信息，但必须明确标注为"基于公开资料"以确保信息透明度。

#### 2.3.2 使用条件限制
- **触发条件**：用户提供材料不足且需要行业背景支撑时使用
- **使用范围**：仅限于提供行业背景、技术趋势、市场现状等支撑信息
- **禁用场景**：不得用于替代用户应提供的核心技术数据

#### 2.3.3 使用原则详解
| 原则 | 具体要求 | 实施方法 | 示例 |
|-----|---------|---------|------|
| 明确区分 | 区分"用户提供数据"和"公开行业数据" | 使用不同的标注格式 | "基于公开行业数据显示..." |
| 来源标注 | 标注信息来源和时效性 | 注明数据来源和发布时间 | "根据2023年工信部报告" |
| 避免冲突 | 严格避免与用户数据产生冲突 | 优先采用用户数据 | 发现冲突时请求用户澄清 |

#### 2.3.4 数据类型与应用场景
- **行业统计数据**：市场规模、增长趋势、竞争格局
- **政策法规信息**：相关政策、标准规范、监管要求
- **技术发展趋势**：技术演进路径、未来发展方向
- **最佳实践案例**：成功案例、经验教训、实施方法

### 2.4 第三优先级：图表参考资源（Chart_Reference_Resources）

#### 2.4.1 获取方式
- **图表示例**：用户提供的现有图表作为格式参考
- **可视化需求**：用户描述的图表类型和展示要求
- **格式规范**：特定申报模板的图表格式要求

#### 2.4.2 使用原则
| 原则 | 具体内容 | 实施要求 |
|-----|---------|---------|
| 格式参考 | 参考用户提供的格式要求 | 保持风格一致性 |
| Mermaid准则 | 遵循Mermaid图表生成准则 | 优先使用Mermaid语法 |
| 专业标准 | 在创新表达的同时保持专业性 | 确保图表的准确性和可读性 |

## 3. 数据处理核心准则

### 3.1 质量控制体系架构

数据处理机制建立在严格的质量控制体系之上，通过多层级验证与标准化流程，确保项目申报材料中所有数据信息的准确性、一致性和可追溯性。

```mermaid
graph TD
    A[数据处理质量控制体系] --> B[输入验证层]
    A --> C[处理控制层]
    A --> D[输出验证层]
    
    B --> B1[数据完整性检查]
    B --> B2[来源可靠性验证]
    B --> B3[格式规范性检查]
    
    C --> C1[数据一致性处理]
    C --> C2[标准化格式转换]
    C --> C3[冲突检测与解决]
    
    D --> D1[准确性最终验证]
    D --> D2[可追溯性确认]
    D --> D3[质量评估报告]
```

### 3.2 核心要求详解

#### 3.2.1 【核心要求】第一信息源原则

**原则定义**：系统优先采用用户在会话过程中明确提供的数据和信息。

**具体实施要求**：
1. **优先级排序**：用户数据 > 公开数据 > 推理数据
2. **缺失处理**：当用户未能提供关键数据时，系统应当明确指出信息缺失状况
3. **主动请求**：通过结构化的方式请求用户补充必要信息

**标准化表述模板**：
```
"根据当前提供的信息，缺少以下关键数据：
1. [具体数据项1] - 用于[具体用途]
2. [具体数据项2] - 用于[具体用途]
建议补充以上信息以完善分析的准确性。"
```

#### 3.2.2 【核心要求】信息诚实原则

**原则定义**：严格禁止任何形式的数据编造、主观推测或所谓"合理假设"行为。

**禁止行为清单**：
- ❌ 编造具体的技术参数或性能数据
- ❌ 基于"常识"推测用户未提供的关键信息
- ❌ 使用"合理假设"来填补数据空白
- ❌ 修改用户提供的原始数据

**正确处理方式**：
- ✅ 明确说明信息不足的具体情况
- ✅ 请求用户提供缺失的关键信息
- ✅ 在分析中明确标注数据来源
- ✅ 对不确定信息进行明确标识

**标准化表述模板**：
```
"当前分析基于已提供的信息，但缺少[具体信息]以支撑[具体分析]。
建议用户提供[具体要求]信息，以确保分析的完整性和准确性。"
```

### 3.3 强烈建议原则

#### 3.3.1 【强烈建议】数据一致性原则

**原则目标**：确保同一数据在不同章节中的表述保持完全一致。

**一致性检查维度**：
| 检查维度 | 具体要求 | 检查方法 | 纠错措施 |
|---------|---------|---------|---------|
| 数值精度 | 小数点位数统一 | 自动检查数值格式 | 统一为最高精度 |
| 计量单位 | 单位表示方法一致 | 建立单位标准库 | 自动转换为标准单位 |
| 时间节点 | 时间格式和基准统一 | 时间格式验证 | 统一为标准时间格式 |
| 术语表达 | 专业术语使用一致 | 术语库对照检查 | 统一为标准术语 |

**实施流程**：
```
步骤1：建立数据字典，记录所有关键数据项
步骤2：在每次使用数据时，查询数据字典确保一致性
步骤3：发现不一致时，追溯到原始来源进行确认
步骤4：更新数据字典，确保后续使用的一致性
```

#### 3.3.2 【强烈建议】来源标注原则

**原则目标**：对每个关键数据进行明确的来源标注，确保信息的可追溯性和透明度。

**标准化标注格式**：
| 信息来源 | 标注格式 | 使用场景 | 示例 |
|---------|---------|---------|------|
| 用户提供材料 | "根据用户提供的XXX材料" | 用户直接提供的数据 | "根据用户提供的测试报告" |
| 公开行业数据 | "基于公开行业数据" | 引用公开发布的数据 | "基于工信部2023年统计数据" |
| 用户口述信息 | "根据用户描述" | 用户口头提供的信息 | "根据用户描述的技术方案" |
| 推理分析结果 | "基于已知信息分析" | 逻辑推理得出的结论 | "基于已知参数分析得出" |

**标注实施要求**：
1. **位置要求**：在首次使用数据时进行标注
2. **格式要求**：使用括号或脚注形式标注
3. **详细程度**：提供足够信息以便追溯验证
4. **更新要求**：数据更新时同步更新标注

## 4. 安全出口机制

### 4.1 机制设计理念

安全出口机制作为系统容错与质量保障的重要组成部分，通过建立标准化的异常情况处理流程，确保在各种复杂场景下都能维持系统的稳定性和输出质量。

### 4.2 异常情况分类与处理

#### 4.2.1 信息不足情况

**触发条件**：
- 用户提供的信息不足以支撑完整分析
- 关键技术参数缺失
- 重要实施细节不明确

**标准化处理流程**：
```
步骤1：识别具体缺失的信息类型和重要程度
步骤2：评估缺失信息对分析质量的影响
步骤3：生成结构化的缺失信息清单
步骤4：向用户提供明确的补充建议
步骤5：在当前分析中标注信息限制
```

**标准化表述模板**：
```
"根据当前提供的信息，缺少以下数据，建议补充以下内容：

【必需信息】
1. [信息项1] - [用途说明] - [重要程度：高/中/低]
2. [信息项2] - [用途说明] - [重要程度：高/中/低]

【建议信息】
1. [信息项3] - [用途说明] - [对分析质量的提升作用]
2. [信息项4] - [用途说明] - [对分析质量的提升作用]

补充以上信息将显著提升分析的准确性和完整性。"
```

#### 4.2.2 数据冲突情况

**冲突类型识别**：
| 冲突类型 | 具体表现 | 检测方法 | 处理策略 |
|---------|---------|---------|---------|
| 数值冲突 | 同一指标的不同数值 | 数值比较算法 | 请求用户确认准确值 |
| 时间冲突 | 时间节点不一致 | 时间逻辑检查 | 建立时间线进行验证 |
| 逻辑冲突 | 前后表述矛盾 | 逻辑一致性检查 | 指出矛盾点请求澄清 |
| 格式冲突 | 数据格式不统一 | 格式规范检查 | 统一为标准格式 |

**标准化处理表述**：
```
"检测到以下数据存在不一致，请确认准确信息：

【冲突详情】
- 位置1：[具体位置] - 数值：[数值A]
- 位置2：[具体位置] - 数值：[数值B]

【影响分析】
此冲突可能影响[具体影响]的准确性。

【建议措施】
请确认准确数值，或提供更详细的说明以解决此冲突。"
```

#### 4.2.3 超出系统能力范围

**能力边界识别**：
- 需要实时数据但系统无法获取
- 需要专业领域深度知识
- 需要复杂的专业计算或仿真
- 涉及保密或敏感信息

**标准化处理方式**：
```
"此项分析需要[具体需求]，超出当前系统处理能力范围。

【建议措施】
1. 咨询相关专业领域专家
2. 使用专业分析工具进行计算
3. 获取最新的实时数据
4. [其他具体建议]

系统将基于现有信息提供初步分析，但建议通过专业渠道进行验证。"
```

#### 4.2.4 格式要求冲突

**冲突场景**：
- 申报模板要求与规则体系格式不一致
- 用户特殊格式要求与标准格式冲突
- 不同标准间的格式要求矛盾

**处理优先级**：
```
1. 申报模板的具体要求（最高优先级）
2. 用户明确的格式要求
3. 本规则体系的指导原则
4. 通用格式标准
```

**标准化处理说明**：
```
"检测到格式要求冲突：
- 申报模板要求：[具体要求]
- 规则体系建议：[具体建议]

【处理决策】
优先满足申报模板要求，原因：[具体原因]

【调整说明】
已按照申报模板要求调整格式，具体调整包括：
1. [调整项1]
2. [调整项2]
```

## 5. 图表生成核心准则

### 5.1 可视化表达机制设计

可视化表达机制通过建立层次化的图表生成标准，确保技术内容能够以最适宜的形式进行呈现，从而提升申报材料的专业性和可读性。

### 5.2 图表生成优先级体系

#### 5.2.1 【强烈建议】Mermaid优先原则

**适用场景**：
- 逻辑流程图：展示工作流程、决策流程
- 程序流图：展示算法逻辑、系统流程
- 简单时序图：展示时间序列、交互过程
- 组织架构图：展示团队结构、管理层级

**实施要求**：
1. **语法规范**：严格遵循Mermaid语法标准
2. **代码包裹**：将生成的代码严格包裹在标准化的代码块标记中
3. **渲染验证**：确保图表能够正确渲染
4. **专业呈现**：保持图表的专业性和美观性

**标准化代码格式**：
````markdown
```mermaid
graph TD
    A[开始] --> B[处理步骤1]
    B --> C{判断条件}
    C -->|是| D[处理步骤2]
    C -->|否| E[处理步骤3]
    D --> F[结束]
    E --> F
```
````

#### 5.2.2 【建议遵循】复杂图表描述原则

**适用场景**：
- 系统架构图：复杂的技术架构
- 网络拓扑图：网络连接关系
- 多维数据图：多变量关系展示
- 专业工程图：需要精确标注的技术图

**描述要求**：
| 描述要素 | 具体内容 | 详细程度 | 示例 |
|---------|---------|---------|------|
| 构成元素 | 图表中的所有组件 | 详细列举每个元素 | "系统包含数据层、逻辑层、表示层" |
| 相互关系 | 元素间的连接关系 | 明确连接类型和方向 | "数据层通过API接口向逻辑层提供数据" |
| 数据流向 | 信息传递的路径 | 清晰的流向描述 | "用户请求从前端传递到后端处理" |
| 技术细节 | 关键技术参数 | 必要的技术规格 | "数据传输采用HTTPS协议" |

**标准化描述模板**：
```
【图表类型】：[图表名称]

【整体结构】：
该图表展示了[整体功能]，主要包含[主要组成部分]。

【核心组件】：
1. [组件1名称]：[功能描述] - [技术特征]
2. [组件2名称]：[功能描述] - [技术特征]
3. [组件3名称]：[功能描述] - [技术特征]

【连接关系】：
- [组件A] → [组件B]：[连接类型] - [数据流向] - [协议/接口]
- [组件B] → [组件C]：[连接类型] - [数据流向] - [协议/接口]

【关键参数】：
- [参数1]：[数值] - [单位] - [说明]
- [参数2]：[数值] - [单位] - [说明]

【实现要点】：
1. [要点1]
2. [要点2]
```

#### 5.2.3 【强烈建议】表格标准化原则

**优先使用表格的数据类型**：
- 技术参数对比
- 进度计划安排
- 预算明细分解
- 人员分工配置
- 风险矩阵分析
- 成果指标体系

**表格设计标准**：
| 设计要素 | 具体要求 | 实施方法 |
|---------|---------|---------|
| 表头设计 | 清晰明确，层次分明 | 使用合并单元格表示层级 |
| 数据对齐 | 数值右对齐，文本左对齐 | 统一对齐方式 |
| 单位标注 | 在表头或数据中明确单位 | 避免单位混乱 |
| 数据精度 | 同类数据保持相同精度 | 统一小数点位数 |
| 颜色使用 | 适度使用颜色区分类别 | 保持专业性 |

### 5.3 可视化层次选择机制

#### 5.3.1 信息复杂度评估

**复杂度分级标准**：
| 复杂度等级 | 信息特征 | 推荐表达方式 | 具体示例 |
|-----------|---------|-------------|---------|
| 简单 | 单一关系，线性逻辑 | 文字表述 | "A导致B" |
| 中等 | 多步骤，有分支 | Mermaid图表 | 工作流程图 |
| 复杂 | 多维关系，复杂结构 | 详细描述 | 系统架构图 |
| 数据密集 | 大量数值，需要对比 | 表格形式 | 性能参数对比 |

#### 5.3.2 选择决策流程

```mermaid
graph TD
    A[信息内容分析] --> B{是否为数值对比?}
    B -->|是| C[使用表格形式]
    B -->|否| D{是否为流程关系?}
    D -->|是| E{复杂度评估}
    E -->|简单| F[文字表述]
    E -->|中等| G[Mermaid图表]
    E -->|复杂| H[详细描述]
    D -->|否| I{是否为结构关系?}
    I -->|是| J{可视化需求}
    J -->|简单| F
    J -->|复杂| H
    I -->|否| F
```

## 相关模块

- **后续模块**：[元指令与主算法](元指令与主算法.md)
- **后续模块**：[全局约束与风格规范](全局约束与风格规范.md)
- **应用模块**：[核心内容生成](../内容生成/核心内容生成.md)

## 更新日志

### v1.0.0 (2025-01-29)
- 初始版本发布
- 建立完整的项目上下文定义框架
- 实现信息获取与处理的标准化流程
- 建立安全出口机制和图表生成准则