# Rules.md 模块依赖关系设计

## 依赖关系概述

本文档定义了 Rules.md 模块化拆分后各模块间的依赖关系、调用顺序和引用机制，确保模块间的协调工作和系统的整体一致性。

## 层级化模块体系

### L0 索引层（Index Layer）
- **00-模块索引.md** - 系统入口，提供全局导航和快速访问

### L1 基础层（Foundation Layer）
- **10-项目上下文与资源定义.md** - 系统基础，所有模块的前提条件
- **11-元指令与主算法.md** - 核心算法，定义基本工作流程
- **12-全局约束与风格规范.md** - 全局规范，统一标准和约束

### L2 功能层（Function Layer）
- **20-核心内容生成.md** - 内容生成核心，依赖基础层
- **21-参数化适应.md** - 适应性调整，依赖内容生成
- **22-核心验证清单.md** - 质量验证，依赖内容生成

### L3 指导层（Guidance Layer）
- **30-章节专项写作指导.md** - 写作指导，依赖内容生成
- **31-数据处理与引用标准.md** - 数据标准，依赖内容生成
- **32-创新点识别与表达框架.md** - 创新表达，依赖内容生成

### L4 实践层（Practice Layer）
- **40-使用指南与最佳实践.md** - 实践指南，整合所有层级

## 详细依赖关系

### 1. 强依赖关系（必须按顺序加载）

#### 1.1 基础依赖链
```
项目上下文与资源定义 → 元指令与主算法 → 全局约束与风格规范
```
- **说明**：基础模块必须按此顺序加载，后续模块依赖前置模块的定义

#### 1.2 功能依赖链
```
核心内容生成 ← (元指令与主算法 + 全局约束与风格规范)
参数化适应 ← 核心内容生成
核心验证清单 ← 核心内容生成
```

#### 1.3 指导依赖链
```
章节专项写作指导 ← 核心内容生成
数据处理与引用标准 ← 核心内容生成  
创新点识别与表达框架 ← 核心内容生成
```

#### 1.4 实践依赖链
```
使用指南与最佳实践 ← (核心验证清单 + 所有指导层模块)
```

### 2. 弱依赖关系（可选引用）

#### 2.1 跨层引用
- 指导层模块可以引用基础层的规范定义
- 实践层可以引用任何层级的具体内容
- 功能层模块间可以相互引用特定功能

#### 2.2 循环引用控制
- **禁止**：同层级模块间的循环依赖
- **允许**：上层模块对下层模块的反向引用（仅限特定场景）

## 模块引用机制

### 1. 内部引用语法

#### 1.1 模块引用格式
```markdown
> **引用模块**：[模块名称](相对路径)
> **相关内容**：参见 [具体章节](模块路径#章节锚点)
```

#### 1.2 实际引用示例
```markdown
> **引用模块**：[项目上下文与资源定义](../核心基础/项目上下文与资源定义.md)
> **相关内容**：参见 [信息获取机制](../核心基础/项目上下文与资源定义.md#信息获取机制)
```

### 2. 外部调用机制

#### 2.1 单模块调用
```
@项目上下文与资源定义.md
```

#### 2.2 依赖链调用
```
@项目上下文与资源定义.md @元指令与主算法.md
```

#### 2.3 功能组调用
```
@核心内容生成.md @参数化适应.md @核心验证清单.md
```

### 3. 智能加载机制

#### 3.1 自动依赖解析
当调用某个模块时，系统应自动加载其依赖模块：

```
调用：@核心内容生成.md
自动加载：
1. 项目上下文与资源定义.md
2. 元指令与主算法.md  
3. 全局约束与风格规范.md
4. 核心内容生成.md
```

#### 3.2 循环依赖检测
系统应检测并阻止循环依赖的产生：
- 在模块加载时进行依赖检查
- 发现循环依赖时给出明确错误提示
- 提供依赖关系可视化工具

## 模块协调机制

### 1. 版本同步机制

#### 1.1 版本兼容性矩阵
```
基础层 v1.0 → 功能层 v1.0-1.2 → 指导层 v1.0-1.1 → 实践层 v1.0
基础层 v1.1 → 功能层 v1.1-1.3 → 指导层 v1.1-1.2 → 实践层 v1.1
```

#### 1.2 版本更新策略
- **向下兼容**：新版本必须兼容旧版本的接口
- **渐进升级**：提供平滑的版本迁移路径
- **依赖通知**：模块更新时通知依赖模块

### 2. 内容一致性保证

#### 2.1 术语统一
- 建立统一的术语表，所有模块共享
- 关键概念在基础层定义，其他层级引用
- 定期检查术语使用的一致性

#### 2.2 标准同步
- 核心标准在基础层定义
- 具体实施标准在功能层细化
- 应用标准在指导层具体化

### 3. 冲突解决机制

#### 3.1 优先级规则
```
基础层 > 功能层 > 指导层 > 实践层
```

#### 3.2 冲突处理流程
1. **检测冲突**：自动或手动发现模块间的冲突
2. **分析影响**：评估冲突对系统的影响范围
3. **制定方案**：根据优先级规则制定解决方案
4. **实施修复**：按照方案修复冲突
5. **验证结果**：确认冲突已解决且无新冲突

## 性能优化策略

### 1. 按需加载
- 只加载当前任务需要的模块
- 实现模块的懒加载机制
- 缓存常用模块以提高响应速度

### 2. 并行处理
- 无依赖关系的模块可以并行加载
- 利用多线程提高加载效率
- 实现模块加载的异步处理

### 3. 缓存策略
- 缓存已解析的依赖关系
- 缓存模块内容以减少重复读取
- 实现智能缓存失效机制

## 监控和维护

### 1. 依赖关系监控
- 定期检查依赖关系的有效性
- 监控模块间的调用频率和性能
- 生成依赖关系分析报告

### 2. 健康检查
- 定期验证所有模块的可用性
- 检查模块间接口的兼容性
- 监控系统整体的稳定性

### 3. 优化建议
- 基于使用数据优化依赖关系
- 识别并消除不必要的依赖
- 持续改进模块间的协调机制

这套依赖关系设计确保了模块化系统的稳定性、可维护性和高效性，为用户提供了清晰的使用指导和强大的功能支持。